#!/usr/bin/env bash
# Script di test per verificare i prerequisiti del bootstrap

echo "🧪 Test prerequisiti bootstrap.sh"
echo "================================="
echo ""

echo "1️⃣ Controllo comandi disponibili..."

# Test comando gh
if command -v gh >/dev/null 2>&1; then
    echo "   ✅ GitHub CLI (gh) disponibile: $(which gh)"
    if gh auth status >/dev/null 2>&1; then
        echo "   ✅ GitHub CLI autenticato"
    else
        echo "   ❌ GitHub CLI non autenticato"
    fi
else
    echo "   ❌ GitHub CLI (gh) non trovato"
fi

# Test comando vercel
if command -v vercel >/dev/null 2>&1; then
    echo "   ✅ Vercel CLI disponibile: $(which vercel)"
    if vercel whoami >/dev/null 2>&1; then
        echo "   ✅ Vercel CLI autenticato"
    else
        echo "   ❌ Vercel CLI non autenticato"
    fi
else
    echo "   ❌ Vercel CLI non trovato"
fi

echo ""
echo "2️⃣ Controllo variabili d'ambiente..."

# Test variabili d'ambiente
vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")
for var in "${vars[@]}"; do
    if [[ -n "${!var:-}" ]]; then
        echo "   ✅ $var configurata"
    else
        echo "   ❌ $var non configurata"
    fi
done

echo ""
echo "3️⃣ Controllo repository Git..."

if [[ -d ".git" ]]; then
    echo "   ✅ Repository Git inizializzato"
    if git remote get-url origin >/dev/null 2>&1; then
        echo "   ✅ Remote origin configurato: $(git remote get-url origin)"
    else
        echo "   ⚠️  Remote origin non configurato"
    fi
else
    echo "   ❌ Repository Git non inizializzato"
fi

echo ""
echo "📋 Riepilogo:"
echo "   Per eseguire bootstrap.sh con successo:"
echo "   1. Installa GitHub CLI: https://cli.github.com/"
echo "   2. Installa Vercel CLI: npm i -g vercel"
echo "   3. Autentica GitHub: gh auth login"
echo "   4. Autentica Vercel: vercel login"
echo "   5. Esporta le 4 variabili d'ambiente richieste:"
echo "      export DATABASE_URL=\"******************************/db\""
echo "      export SUPABASE_URL=\"https://your-project.supabase.co\""
echo "      export SUPABASE_ANON_KEY=\"eyJhbGciOi...\""
echo "      export SUPABASE_SERVICE_ROLE_KEY=\"eyJhbGciOi...\""
echo "   6. Esegui: ./scripts/bootstrap.sh"
echo ""
