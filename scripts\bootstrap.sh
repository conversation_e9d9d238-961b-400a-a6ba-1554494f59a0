#!/usr/bin/env bash
# ==================================================================
# Script: bootstrap.sh
# Scopo : Bootstrap intelligente progetto rebuild-link con check Supabase
# Crea  : 1. Repository GitHub "rebuild-link" (pubblico)
#         2. Progetto Vercel "rebuild-link" (Next.js)
#         3. Vercel env variables (solo se Supabase non già configurato)
#         4. GitHub Secrets (sempre, sovrascrive esistenti)
# ------------------------------------------------------------------
# Prerequisiti:
#   • GitHub CLI (`gh`) autenticato
#   • Vercel CLI (`vercel`) autenticato
#   • jq installato (per parsing JSON)
#   • Quattro variabili d'ambiente esportate:
#     - DATABASE_URL
#     - SUPABASE_URL
#     - SUPABASE_ANON_KEY
#     - SUPABASE_SERVICE_ROLE_KEY
# ==================================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

echo "🚀 Bootstrap ReBuild Link - Setup Intelligente"
echo "==============================================="
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# VALIDAZIONE PREREQUISITI
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "🔍 Validazione prerequisiti..."

# Controlla comandi necessari
for cmd in gh vercel jq; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
        echo "❌ Comando $cmd non trovato"
        echo "💡 Installa prima i prerequisiti:"
        echo "   • GitHub CLI: https://cli.github.com/"
        echo "   • Vercel CLI: npm i -g vercel"
        echo "   • jq: apt install jq / brew install jq"
        exit 1
    fi
    echo "   ✅ $cmd disponibile"
done

# Controlla autenticazione GitHub
if ! gh auth status >/dev/null 2>&1; then
    echo "❌ GitHub CLI non autenticato"
    echo "🔑 Esegui prima: gh auth login"
    exit 1
fi
echo "   ✅ GitHub CLI autenticato"

# Controlla autenticazione Vercel
if ! vercel whoami >/dev/null 2>&1; then
    echo "❌ Vercel CLI non autenticato"
    echo "🔑 Esegui prima: vercel login"
    exit 1
fi
echo "   ✅ Vercel CLI autenticato"

# Controlla variabili d'ambiente richieste
required_vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        missing_vars+=("$var")
    else
        echo "   ✅ $var configurata"
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "❌ Variabili d'ambiente mancanti: ${missing_vars[*]}"
    echo "💡 Esporta le variabili richieste prima di eseguire lo script"
    exit 1
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 0: REPOSITORY GITHUB "rebuild-link"
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "📦 Step 0: Setup repository GitHub..."

# Crea repository "rebuild-link" pubblico solo se non esiste
if gh repo view rebuild-link 2>/dev/null; then
    echo "   ✅ Repository rebuild-link già esistente"
else
    echo "   🔧 Creazione repository rebuild-link..."

    # Inizializza git se necessario
    if [[ ! -d ".git" ]]; then
        echo "   🔧 Inizializzazione repository Git..."
        git init
        git add .
        git commit -m "feat: initial commit - rebuild-link project"
    fi

    # Crea repository GitHub pubblico
    gh repo create rebuild-link --public --source=. --remote=origin --push
    echo "   ✅ Repository rebuild-link creato e collegato"
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 1: PROGETTO VERCEL "rebuild-link" (Next.js)
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "🚀 Step 1: Setup progetto Vercel..."

# Collega/crea progetto Vercel "rebuild-link" (Next.js)
echo "   🔗 Collegamento progetto Vercel..."
vercel link --yes

# Forza framework Next.js
echo "   ⚙️  Configurazione framework Next.js..."
vercel project link set-framework nextjs
echo "   ✅ Framework Next.js configurato"

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 2: VERCEL ENV VARIABLES (solo se Supabase non già configurato)
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "🌱 Step 2: Controllo variabili d'ambiente Vercel..."

# Controlla se SUPABASE_URL è già configurata in production
echo "   🔍 Controllo configurazione Supabase esistente..."
existing_keys=$(vercel env ls production --json 2>/dev/null | jq -r '.[].key' 2>/dev/null || echo "")

if echo "$existing_keys" | grep -q "SUPABASE_URL"; then
    echo "   ⚠️  Supabase già configurato su Vercel (SUPABASE_URL trovata)"
    echo "   ⏭️  Saltando configurazione variabili d'ambiente Vercel"
    echo "   💡 Per riconfigurare, rimuovi prima le variabili SUPABASE_* da Vercel"
else
    echo "   ✅ Supabase non ancora configurato, procedo con la configurazione..."

    # Configura variabili per production e preview
    environments=("production" "preview")
    env_vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")

    for ENV in "${environments[@]}"; do
        echo "   🌱 Configurazione ambiente: $ENV"

        for var in "${env_vars[@]}"; do
            echo "   📝 Aggiungendo $var per $ENV..."
            # Usa printf per pipe più robusto
            if printf '%s\n' "${!var}" | vercel env add "$var" "$ENV" --yes >/dev/null 2>&1; then
                echo "   ✅ $var configurata per $ENV"
            else
                echo "   ⚠️  $var già esistente per $ENV (skip)"
            fi
        done
    done

    echo "   ✅ Configurazione Vercel completata"
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 3: GITHUB SECRETS (sempre, sovrascrive esistenti)
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "🔑 Step 3: Configurazione GitHub Secrets..."

# Imposta i 4 secrets richiesti (sempre, sovrascrive esistenti)
secrets=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")

for secret in "${secrets[@]}"; do
    echo "   🔐 Impostando secret: $secret"
    gh secret set "$secret" -b"${!secret}"
    echo "   ✅ Secret $secret configurato"
done

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# COMPLETAMENTO
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "✅ Bootstrap completato (repo, vercel, secrets)"
echo ""
echo "📋 Configurazione completata:"
echo "   • Repository GitHub: rebuild-link (pubblico)"
echo "   • Progetto Vercel: rebuild-link (Next.js)"
echo "   • GitHub Secrets: 4 variabili configurate/aggiornate"
echo "   • Vercel Env: configurazione intelligente completata"
echo ""
echo "🚀 Prossimi passi:"
echo "   • Verifica secrets: gh secret list"
echo "   • Verifica env Vercel: vercel env ls production"
echo "   • Test deploy: git push origin main"
echo ""
echo "💡 Nota: Se Supabase era già configurato su Vercel, la configurazione è stata saltata."
echo "   Per riconfigurare, rimuovi prima le variabili SUPABASE_* da Vercel Dashboard."
echo ""
