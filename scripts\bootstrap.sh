#!/bin/bash
# ==============================================================================
# Bootstrap Script: GitHub Repo, Vercel Project, and Secrets
# ==============================================================================
# Questo script automatizza il setup iniziale di un progetto:
# 1. Crea un repository GitHub pubblico "rebuild-link" se non esiste.
# 2. Collega il progetto a Vercel, creandolo se necessario.
# 3. Configura i secrets su GitHub Actions.
# 4. Configura le variabili d'ambiente su Vercel per production e preview.
#
# Prerequisiti:
#   - `gh` (GitHub CLI) installato e autenticato.
#   - `vercel` (Vercel CLI) installato e autenticato.
#   - Le seguenti variabili d'ambiente devono essere esportate:
#     - DATABASE_URL
#     - SUPABASE_URL
#     - SUPABASE_ANON_KEY
#     - SUPABASE_SERVICE_ROLE_KEY
# ==============================================================================

set -euo pipefail

# --- Funzioni di utility e colori ---
Color_Off='\033[0m'
BGreen='\033[1;32m'
BRed='\033[1;31m'
BYellow='\033[1;33m'

info() {
  echo -e "${BYellow}[INFO]${Color_Off} $1"
}

success() {
  echo -e "${BGreen}[SUCCESS]${Color_Off} $1"
}

error() {
  echo -e "${BRed}[ERROR]${Color_Off} $1" >&2
  exit 1
}

# --- 0. Verifica Prerequisiti ---
info "Verifica dei prerequisiti..."

# Controlla CLI
command -v gh >/dev/null 2>&1 || error "GitHub CLI (gh) non trovato. Installalo per continuare."
command -v vercel >/dev/null 2>&1 || error "Vercel CLI (vercel) non trovato. Installalo per continuare."

# Controlla autenticazione
gh auth status >/dev/null 2>&1 || error "Non sei autenticato con GitHub CLI. Esegui 'gh auth login'."
vercel whoami >/dev/null 2>&1 || error "Non sei autenticato con Vercel CLI. Esegui 'vercel login'."

# Controlla variabili d'ambiente
[[ -z "${DATABASE_URL-}" ]] && error "Variabile d'ambiente DATABASE_URL non impostata."
[[ -z "${SUPABASE_URL-}" ]] && error "Variabile d'ambiente SUPABASE_URL non impostata."
[[ -z "${SUPABASE_ANON_KEY-}" ]] && error "Variabile d'ambiente SUPABASE_ANON_KEY non impostata."
[[ -z "${SUPABASE_SERVICE_ROLE_KEY-}" ]] && error "Variabile d'ambiente SUPABASE_SERVICE_ROLE_KEY non impostata."

success "Prerequisiti verificati."

# --- 1. Crea Repository GitHub ---
info "Controllo e creazione repository GitHub 'rebuild-link'..."
if gh repo view rebuild-link >/dev/null 2>&1; then
  info "Il repository 'rebuild-link' esiste già."
else
  info "Creazione repository 'rebuild-link'..."
  gh repo create rebuild-link --public --source=. --remote=origin --push
  success "Repository GitHub 'rebuild-link' creato e codice pushato."
fi

# --- 2. Collega Progetto Vercel ---
info "Collegamento al progetto Vercel 'rebuild-link'..."
# Il flag --yes accetta le impostazioni di default, creando il progetto se non esiste.
# Il framework Next.js viene solitamente rilevato in automatico.
vercel link --yes
success "Progetto collegato a Vercel."

# --- 3. Configura GitHub Secrets ---
info "Configurazione dei secrets su GitHub Actions..."
gh secret set DATABASE_URL --body "$DATABASE_URL"
gh secret set SUPABASE_URL --body "$SUPABASE_URL"
gh secret set SUPABASE_ANON_KEY --body "$SUPABASE_ANON_KEY"
gh secret set SUPABASE_SERVICE_ROLE_KEY --body "$SUPABASE_SERVICE_ROLE_KEY"
success "4 secrets configurati su GitHub."

# --- 4. Configura Vercel Environment Variables ---
info "Configurazione delle variabili d'ambiente su Vercel..."
for ENV in production preview; do
  info "Aggiunta variabili per l'ambiente '$ENV'..."
  vercel env add DATABASE_URL "$DATABASE_URL" "$ENV" --yes
  vercel env add SUPABASE_URL "$SUPABASE_URL" "$ENV" --yes
  vercel env add SUPABASE_ANON_KEY "$SUPABASE_ANON_KEY" "$ENV" --yes
  # Nota: SUPABASE_SERVICE_ROLE_KEY è un secret per la CI/CD e non viene esposto a Vercel tramite questo script.
done
success "Variabili d'ambiente configurate per production e preview su Vercel."

# --- 5. Messaggio Finale ---
echo
success "✅ Bootstrap completato!"
info "La tua infrastruttura CI/CD e di deployment è pronta."
info "Esegui 'git push' per avviare la tua prima pipeline."