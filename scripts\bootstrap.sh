#!/usr/bin/env bash
# ==================================================================
# Script: bootstrap.sh
# Scopo : Script semplificato per bootstrap progetto rebuild-link
# Crea  : 1. Repository GitHub "rebuild-link" (pubblico)
#         2. Progetto Vercel "rebuild-link" (Next.js)
#         3. GitHub Secrets (4 variabili)
#         4. Vercel env variables (production + preview)
# ------------------------------------------------------------------
# Prerequisiti:
#   • GitHub CLI (`gh`) autenticato
#   • Vercel CLI (`vercel`) autenticato
#   • Quattro variabili d'ambiente esportate:
#     - DATABASE_URL
#     - SUPABASE_URL
#     - SUPABASE_ANON_KEY
#     - SUPABASE_SERVICE_ROLE_KEY
# ==================================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

echo "🚀 Bootstrap ReBuild Link - Setup Automatico"
echo "============================================"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 1. VALIDAZIONE PREREQUISITI
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "1️⃣  Validazione prerequisiti..."

# Controlla comandi necessari
for cmd in gh vercel; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
        echo "❌ Comando $cmd non trovato"
        echo "💡 Installa prima i prerequisiti:"
        echo "   • GitHub CLI: https://cli.github.com/"
        echo "   • Vercel CLI: npm i -g vercel"
        exit 1
    fi
    echo "   ✅ $cmd disponibile"
done

# Controlla autenticazione GitHub
if ! gh auth status >/dev/null 2>&1; then
    echo "❌ GitHub CLI non autenticato"
    echo "🔑 Esegui prima: gh auth login"
    exit 1
fi
echo "   ✅ GitHub CLI autenticato"

# Controlla autenticazione Vercel
if ! vercel whoami >/dev/null 2>&1; then
    echo "❌ Vercel CLI non autenticato"
    echo "🔑 Esegui prima: vercel login"
    exit 1
fi
echo "   ✅ Vercel CLI autenticato"

# Controlla variabili d'ambiente richieste
required_vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        missing_vars+=("$var")
    else
        echo "   ✅ $var configurata"
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "❌ Variabili d'ambiente mancanti: ${missing_vars[*]}"
    echo "💡 Esporta le variabili richieste prima di eseguire lo script"
    exit 1
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 2. CREAZIONE REPOSITORY GITHUB
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "2️⃣  Setup repository GitHub..."

# Crea repository "rebuild-link" pubblico solo se non esiste
if gh repo view rebuild-link >/dev/null 2>&1; then
    echo "   📦 Repository rebuild-link già esistente"
else
    echo "   📦 Creazione repository rebuild-link..."
    
    # Inizializza git se necessario
    if [[ ! -d ".git" ]]; then
        echo "   🔧 Inizializzazione repository Git..."
        git init
        git add .
        git commit -m "feat: initial commit - rebuild-link project"
    fi
    
    # Crea repository GitHub pubblico
    gh repo create rebuild-link --public --source=. --remote=origin --push
    echo "   ✅ Repository rebuild-link creato e collegato"
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 3. SETUP PROGETTO VERCEL
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "3️⃣  Setup progetto Vercel..."

# Collega/crea progetto Vercel "rebuild-link" (Next.js)
echo "   🔗 Collegamento progetto Vercel..."
vercel link --yes

# Imposta framework Next.js (se supportato dal comando)
echo "   ⚙️  Configurazione framework Next.js..."
if vercel project ls | grep -q "rebuild-link"; then
    echo "   ✅ Progetto Vercel rebuild-link configurato"
else
    echo "   📦 Creazione progetto Vercel rebuild-link..."
    # Il comando vercel link dovrebbe aver già gestito la creazione
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 4. CONFIGURAZIONE GITHUB SECRETS
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "4️⃣  Configurazione GitHub Secrets..."

# Imposta i 4 secrets richiesti
secrets=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")

for secret in "${secrets[@]}"; do
    echo "   🔑 Impostando secret: $secret"
    gh secret set "$secret" -b"${!secret}"
    echo "   ✅ Secret $secret configurato"
done

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 5. CONFIGURAZIONE VERCEL ENV VARIABLES
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "5️⃣  Configurazione Vercel Environment Variables..."

# Configura variabili per production e preview
environments=("production" "preview")
env_vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY")

for ENV in "${environments[@]}"; do
    echo "   🌱 Configurazione ambiente: $ENV"
    
    for var in "${env_vars[@]}"; do
        echo "   📝 Aggiungendo $var per $ENV..."
        # Usa echo per passare il valore via stdin a vercel env add
        if echo "${!var}" | vercel env add "$var" "$ENV" -y >/dev/null 2>&1; then
            echo "   ✅ $var configurata per $ENV"
        else
            echo "   ⚠️  $var già esistente per $ENV (skip)"
        fi
    done
done

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 6. COMPLETAMENTO
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "✅ Bootstrap completato!"
echo ""
echo "📋 Configurazione completata:"
echo "   • Repository GitHub: rebuild-link (pubblico)"
echo "   • Progetto Vercel: rebuild-link (Next.js)"
echo "   • GitHub Secrets: 4 variabili configurate"
echo "   • Vercel Env: production + preview configurati"
echo ""
echo "🚀 Prossimi passi:"
echo "   • Verifica secrets: gh secret list"
echo "   • Verifica env Vercel: vercel env ls"
echo "   • Test deploy: git push origin main"
echo ""
